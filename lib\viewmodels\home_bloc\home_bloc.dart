import 'dart:convert';

import 'package:equatable/equatable.dart';

import 'package:room_eight/core/utils/app_exports.dart';

import 'package:room_eight/models/common_model/common_model.dart';
import 'package:room_eight/models/home_model/block_profile_model.dart';
import 'package:room_eight/models/home_model/detailed_profile_model.dart';
import 'package:room_eight/models/home_model/get_all_profile_model.dart';
import 'package:room_eight/models/home_model/report_profile_model.dart';
import 'package:room_eight/models/profile_model/profile_model.dart';

import 'package:room_eight/repository/home_repository/home_repository.dart';
import 'package:room_eight/repository/profile_repository/profile_repository.dart';
import 'package:swipable_stack/swipable_stack.dart';

part 'home_event.dart';
part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final HomeRepository homeRepository;
  final ProfileRepository profileRepository;

  HomeBloc(this.homeRepository, this.profileRepository)
    : super(
        HomeState(
          // users: _sampleUsers,
          currentIndex: 0,
          pageController: PageController(),
          isPulseAnimating: false,
          carouselCurrentIndex: 0,
          hasSeenTutorial: true,
          swipableController: SwipableStackController(),
          reportController: TextEditingController(),
        ),
      ) {
    on<GetAllProfile>(_onLoadHomePage);
    on<HomePageChanged>(_onPageChanged);
    on<HomeResetToFirst>(_onResetToFirst);
    on<AcceptUser>(_onAcceptUser);
    on<RejectUser>(_onRejectUser);
    on<StartPulseAnimation>(_onStartPulseAnimation);
    on<StopPulseAnimation>(_onStopPulseAnimation);
    on<CarouselPageChanged>(_onCarouselPageChanged);
    on<CheckTutorialStatus>(_onCheckTutorialStatus);
    on<DismissTutorial>(_onDismissTutorial);
    on<InitializeSwipableController>(_onInitializeSwipableController);
    on<DisposeSwipableController>(_onDisposeSwipableController);
    on<GetUserProfileByID>(_onGetUserProfileByID);
    on<ReportChange>(_onReportChange);
    on<CurrentProfileIdChange>(_onCurrentProfileIdChange);
    on<BlockProfileSubmit>(_onBlockProfileSubmit);
    on<ReportProfileSubmit>(_onReportProfileSubmit);
    // Initialize tutorial status when bloc is created
    add(const CheckTutorialStatus());
  }

  void _onLoadHomePage(GetAllProfile event, Emitter<HomeState> emit) async {
    emit(state.copyWith(loadHomePageData: true));

    // Load both user data and selection options
    final futures = await Future.wait([
      homeRepository.getAllUserData(),
      profileRepository.getSelectionMenu(),
    ]);

    final UserDataModel userResult = futures[0] as UserDataModel;
    final ProfileModel selectionResult = futures[1] as ProfileModel;

    if (userResult.status == true) {
      List<UserData> data = userResult.users ?? [];

      // Extract selection options if available
      List<ProfileOptionModel> habitsLifestyle = [];
      List<ProfileOptionModel> livingStyle = [];
      List<ProfileOptionModel> interestsHobbies = [];

      if (selectionResult.status == true && selectionResult.data != null) {
        habitsLifestyle = selectionResult.data!.habitsLifestyle ?? [];
        livingStyle = selectionResult.data!.livingStyle ?? [];
        interestsHobbies = selectionResult.data!.interestsHobbies ?? [];

        // Debug logging
        Logger.lOG("Selection options loaded:");
        Logger.lOG("Habits count: ${habitsLifestyle.length}");
        Logger.lOG("Living count: ${livingStyle.length}");
        Logger.lOG("Interests count: ${interestsHobbies.length}");
      }

      emit(
        state.copyWith(
          users: data,
          habitsLifestyle: habitsLifestyle,
          livingStyle: livingStyle,
          interestsHobbies: interestsHobbies,
          loadHomePageData: false,
        ),
      );
    } else {
      Logger.lOG("Home Page All User Data Not load");
      emit(state.copyWith(loadHomePageData: false));
    }
  }

  void _onPageChanged(HomePageChanged event, Emitter<HomeState> emit) {
    emit(state.copyWith(currentIndex: event.index));
  }

  void _onResetToFirst(HomeResetToFirst event, Emitter<HomeState> emit) {
    if (state.currentIndex != 0) {
      state.pageController.animateToPage(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      emit(state.copyWith(currentIndex: 0));
    }
  }

  void _onAcceptUser(AcceptUser event, Emitter<HomeState> emit) async {
    final Map<String, dynamic> data = {'like_profile_id': event.userId};
    CommonModel result = await homeRepository.likeUserProfile(data);
    if (result.status == true) {
      final updatedUsers = state.users
          .where((u) => u.id != event.userId)
          .toList();
      emit(state.copyWith(users: updatedUsers));
    } else {
      Logger.lOG("Like Profile Not Work");
    }
  }

  void _onRejectUser(RejectUser event, Emitter<HomeState> emit) async {
    final Map<String, dynamic> data = {'dislike_profile_id': event.userId};
    CommonModel result = await homeRepository.dislikeUserProfile(data);
    if (result.status == true) {
      final updatedUsers = state.users
          .where((u) => u.id != event.userId)
          .toList();
      emit(state.copyWith(users: updatedUsers));
    } else {
      Logger.lOG("Like Profile Not Work");
    }
  }

  void _onStartPulseAnimation(
    StartPulseAnimation event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(isPulseAnimating: true));
  }

  void _onStopPulseAnimation(
    StopPulseAnimation event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(isPulseAnimating: false));
  }

  void _onCarouselPageChanged(
    CarouselPageChanged event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(carouselCurrentIndex: event.index));
  }

  Future<void> _onCheckTutorialStatus(
    CheckTutorialStatus event,
    Emitter<HomeState> emit,
  ) async {
    await Future.delayed(Duration.zero);
    final seen =
        Prefobj.preferences?.get(Prefkeys.SWIPE_TUTORIAL_SHOWN) ?? false;
    emit(state.copyWith(hasSeenTutorial: seen));
  }

  Future<void> _onDismissTutorial(
    DismissTutorial event,
    Emitter<HomeState> emit,
  ) async {
    await Prefobj.preferences?.put(Prefkeys.SWIPE_TUTORIAL_SHOWN, true);
    emit(state.copyWith(hasSeenTutorial: true));
  }

  void _onInitializeSwipableController(
    InitializeSwipableController event,
    Emitter<HomeState> emit,
  ) {
    final controller = SwipableStackController();
    emit(state.copyWith(swipableController: controller));
  }

  void _onDisposeSwipableController(
    DisposeSwipableController event,
    Emitter<HomeState> emit,
  ) {
    state.swipableController?.dispose();
    emit(state.copyWith(swipableController: null));
  }

  void _onGetUserProfileByID(
    GetUserProfileByID event,
    Emitter<HomeState> emit,
  ) async {
    emit(state.copyWith(isloadUserDetail: true));

    DetailedProfileModel result = await homeRepository.getUserProfileById(
      id: event.userId,
    );

    if (result.status == true) {
      final ProfileData? data = result.data;

      final List<int> habits = List<int>.from(
        json.decode(data?.habitsLifestyle ?? "[]"),
      );
      final List<int> living = List<int>.from(
        json.decode(data?.livingStyle ?? "[]"),
      );
      final List<int> interests = List<int>.from(
        json.decode(data?.interestsHobbies ?? "[]"),
      );

      final filteredHabits = state.habitsLifestyle
          .where((e) => habits.contains(e.id))
          .toList();
      final filteredLiving = state.livingStyle
          .where((e) => living.contains(e.id))
          .toList();
      final filteredInterests = state.interestsHobbies
          .where((e) => interests.contains(e.id))
          .toList();

      final combinedSelections = [
        ...filteredHabits,
        ...filteredLiving,
        ...filteredInterests,
      ];
      emit(
        state.copyWith(
          isloadUserDetail: false,
          userProfileDetail: data,
          userOptionsData: combinedSelections,
        ),
      );
    } else {
      emit(state.copyWith(isloadUserDetail: false, userOptionsData: []));
    }
  }

  void _onReportChange(ReportChange event, Emitter<HomeState> emit) {
    state.reportController.text = event.report;
    emit(state.copyWith(reportController: state.reportController));
  }

  void _onCurrentProfileIdChange(
    CurrentProfileIdChange event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(currentProfileId: event.index));
  }

  void _onReportProfileSubmit(
    ReportProfileSubmit event,
    Emitter<HomeState> emit,
  ) async {
    final Map<String, dynamic> data = {
      "profile_id": event.userId,
      "reason": state.reportController.text.trim(),
    };
    ReportProfileModel result = await homeRepository.reportProfile(data: data);
    // emit(state.copyWith(reportController: state.reportController));
    state.reportController.clear();
    if (result.status == true) {
    } else {
      Logger.lOG("User Profile is not reported.");
    }
  }

  void _onBlockProfileSubmit(
    BlockProfileSubmit event,
    Emitter<HomeState> emit,
  ) async {
    final Map<String, dynamic> data = {"profile_id": event.userId};
    BlockProfileModel result = await homeRepository.blockProfile(data: data);

    if (result.status == true) {
    } else {
      Logger.lOG("User Profile is not blocked.");
    }
  }
}
